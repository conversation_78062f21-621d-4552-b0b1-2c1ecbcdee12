const fs = require("fs");
const csv = require("csv-parser");
const OpenAI = require("openai");
const path = require("path");
require("dotenv").config();

// Initialiser l'API OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Fonction pour parser un fichier CSV
function parseCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];

    fs.createReadStream(filePath)
      .on("error", (error) => {
        reject(error);
      })
      .pipe(csv())
      .on("data", (data) => {
        results.push(data);
      })
      .on("end", () => {
        console.log(`CSV parsing completed. ${results.length} records found.`);
        resolve(results);
      });
  });
}

// Fonction pour générer une image avec OpenAI
async function generateImage(prompt, ean) {
  try {
    console.log(`Generating image for EAN: ${ean}`);

    const response = await openai.responses.create({
      model: "gpt-4.1-mini",
      input:
        "Generate an image of gray tabby cat hugging an otter with an orange scarf",
      tools: [{ type: "image_generation" }],
    });

    const imageUrl = response.data[0].url;

    // Télécharger l'image depuis l'URL
    const imageResponse = await fetch(imageUrl);
    const imageBuffer = await imageResponse.arrayBuffer();

    // Créer le dossier de sortie s'il n'existe pas
    const outputDir = "./images";
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }

    // Sauvegarder l'image
    const imagePath = path.join(outputDir, `${ean}.png`);
    fs.writeFileSync(imagePath, Buffer.from(imageBuffer));

    console.log(`Image saved to ${imagePath}`);
    return imagePath;
  } catch (error) {
    console.error(`Error generating image for EAN ${ean}:`, error.message);
    throw error;
  }
}

// Fonction pour traiter les données en batch
async function processBatch(data, batchSize = 5) {
  const results = [];

  for (let i = 0; i < data.length; i += batchSize) {
    const batch = data.slice(i, i + batchSize);
    console.log(
      `Processing batch ${i / batchSize + 1} of ${Math.ceil(
        data.length / batchSize
      )}`
    );

    // Traiter chaque élément du batch en parallèle
    const batchPromises = batch.map((item) =>
      generateImage(item.prompt, item.EAN)
        .then((imagePath) => ({ EAN: item.EAN, imagePath, success: true }))
        .catch((error) => ({
          EAN: item.EAN,
          error: error.message,
          success: false,
        }))
    );

    // Attendre que toutes les générations du batch soient terminées
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Pause entre les batchs pour éviter de dépasser les limites de l'API
    if (i + batchSize < data.length) {
      console.log("Waiting 2 seconds before next batch...");
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
  }

  return results;
}

// Fonction principale
async function main() {
  try {
    // Charger les données du CSV
    const data = await parseCSV("./data.csv");
    console.log("CSV data loaded successfully");

    // Traiter les données en batch
    const results = await processBatch(data, 3); // Batch de 3 à la fois

    // Résumé des résultats
    const successful = results.filter((r) => r.success).length;
    console.log(
      `Processing completed. ${successful}/${results.length} images generated successfully.`
    );

    // Sauvegarder les résultats dans un fichier JSON
    fs.writeFileSync("./results.json", JSON.stringify(results, null, 2));
    console.log("Results saved to results.json");
  } catch (error) {
    console.error("Error in main process:", error);
  }
}

// Exporter les fonctions pour les utiliser dans d'autres fichiers
module.exports = { parseCSV, generateImage, processBatch };

// Exécuter si appelé directement
if (require.main === module) {
  main();
}
